using AutoMapper;
using MediatR;
using Microsoft.EntityFrameworkCore;
using System.Linq.Expressions;
using TmobCore.Cms.Application.Contracts.Logging;
using TmobCore.Cms.Application.Contracts.Persistence;
using TmobCore.Cms.Application.Contracts.User;
using TmobCore.Cms.Application.Helper;
using TmobCore.Cms.Application.Models.Airport;
using TmobCore.Cms.Application.Models.Common;
using TmobCore.Cms.Domain.Entities;

namespace TmobCore.Cms.Application.Features.Airport.Queries.GetAirports
{
    public class GetAirportsQueryHandler : IRequestHandler<GetAirportsQuery, ActionResponse<List<AirportResponse>>>
    {
        private readonly IUserPrincipal _userPrincipal;
        private readonly IAirportRepository _airportRepository;
        private readonly IMapper _mapper;
        private readonly IAppLogger<GetAirportsQueryHandler> _logger;

        public GetAirportsQueryHandler(
            IUserPrincipal userPrincipal,
            IAirportRepository airportRepository,
            IMapper mapper,
            IAppLogger<GetAirportsQueryHandler> logger)
        {
            _userPrincipal = userPrincipal;
            _airportRepository = airportRepository;
            _mapper = mapper;
            _logger = logger;
        }

        public async Task<ActionResponse<List<AirportResponse>>> Handle(GetAirportsQuery request, CancellationToken cancellationToken)
        {
            try
            {
                var projectId = _userPrincipal.ProjectId.ToGuid();

                var (airportsQuery, totalCount) = _airportRepository.GetPaginatedQueryWithSearch(
                    BuildPredicate(request, projectId),
                    request.SearchTerm,
                    request.Page,
                    request.PageSize,
                    inc => inc.Image,
                    inc => inc.City,
                    inc => inc.City.Country
                );

                var airportList = await airportsQuery
                    .AsNoTracking()
                    .OrderBy(x => x.Order)
                    .ThenBy(x => x.DateCreated)
                    .ToListAsync(cancellationToken);

                var airportResponse = _mapper.Map<List<AirportResponse>>(airportList);

                return ActionResponse<List<AirportResponse>>
                    .Success(airportResponse, StatusCode.Ok)
                    .WithPagination(request.Page, request.PageSize, totalCount);
            }
            catch (Exception ex)
            {
                _logger.LogError("GetAirportsQueryHandler => Failed to retrieve airports", ex);
                return ActionResponse<List<AirportResponse>>.Fail(
                    "An error occurred while retrieving airports.",
                    StatusCode.InternalServerError);
            }
        }

        private static Expression<Func<Domain.Entities.Airport, bool>> BuildPredicate(GetAirportsQuery request, Guid? projectId)
        {
            return x =>
                !x.IsDeleted &&
                x.IsActive &&
                (projectId == null || x.ProjectId == projectId) &&
                (!request.GroupId.HasValue || x.GroupId == request.GroupId.Value) &&
                (!request.LanguageId.HasValue || x.LanguageId == request.LanguageId.Value);
        }
    }
}
