{"examples": [{"description": "Güvenlik prosedürleri sayfası oluşturma örneği", "request": {"pages": [{"title": "Güvenlik Prosedürleri", "slug": "guvenlik-<PERSON><PERSON><PERSON><PERSON>", "type": "security-procedures", "icon": "Shield", "order": 1, "status": 1, "publishDate": "2025-07-03T16:00:00Z", "languageId": "550e8400-e29b-41d4-a716-446655440000", "groupId": "550e8400-e29b-41d4-a716-446655440001", "imageId": "550e8400-e29b-41d4-a716-446655440002", "contents": [{"content": "<h2>Güvenlik Prosedürleri</h2><p>Bu sayfada güvenlik ile ilgili tüm prosedürler yer almaktadır.</p>", "order": 1}]}]}}, {"description": "Hizmet prosedürleri sayfası oluşturma örneği", "request": {"pages": [{"title": "Hizmet Prosedürleri", "slug": "hizmet-prose<PERSON><PERSON><PERSON>", "type": "service-procedures", "icon": "Settings", "order": 2, "status": 1, "publishDate": "2025-07-03T16:00:00Z", "languageId": "550e8400-e29b-41d4-a716-446655440000", "groupId": "550e8400-e29b-41d4-a716-446655440001", "contents": [{"content": "<h2>Hizmet Prosedürleri</h2><p>Hizmet süreçleri ve prosedürleri hakkında bilgiler.</p>", "order": 1}]}]}}, {"description": "Acil durum prosedürleri sayfası oluşturma örneği", "request": {"pages": [{"title": "Acil Durum Prosedürleri", "slug": "acil-durum-prosedurleri", "type": "emergency-procedures", "icon": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "order": 3, "status": 1, "publishDate": "2025-07-03T16:00:00Z", "languageId": "550e8400-e29b-41d4-a716-446655440000", "groupId": "550e8400-e29b-41d4-a716-446655440001", "contents": [{"content": "<h2>Acil Durum Prosedürleri</h2><p>Acil durumlar için u<PERSON>gulanması gereken prosedürler.</p>", "order": 1}]}]}}, {"description": "Çoklu sayfa oluşturma örneği - Aynı tip", "request": {"pages": [{"title": "Temel Güvenlik Kuralları", "slug": "temel-guvenlik-kurallari", "type": "security-procedures", "icon": "Shield", "order": 1, "status": 1, "publishDate": "2025-07-03T16:00:00Z", "languageId": "550e8400-e29b-41d4-a716-446655440000", "groupId": "550e8400-e29b-41d4-a716-446655440001", "contents": [{"content": "<h2>Temel Güvenlik Kuralları</h2><p>Herkesin bilmesi gereken temel güvenlik kuralları.</p>", "order": 1}]}, {"title": "Gelişmiş Güvenlik Protokolleri", "slug": "gelismis-guvenlik-protokolleri", "type": "security-procedures", "icon": "ShieldCheck", "order": 2, "status": 1, "publishDate": "2025-07-03T16:00:00Z", "languageId": "550e8400-e29b-41d4-a716-446655440000", "groupId": "550e8400-e29b-41d4-a716-446655440001", "contents": [{"content": "<h2>Gelişmiş Güvenlik Protokolleri</h2><p>İleri düzey güvenlik protokolleri ve uygulamaları.</p>", "order": 1}]}]}}, {"description": "Farklı kategorilerde sayfa oluşturma örneği", "request": {"pages": [{"title": "Müşteri Hizmetleri Rehberi", "slug": "musteri-hizmetleri-re<PERSON>beri", "type": "customer-service", "icon": "Users", "order": 1, "status": 1, "publishDate": "2025-07-03T16:00:00Z", "languageId": "550e8400-e29b-41d4-a716-446655440000", "groupId": "550e8400-e29b-41d4-a716-446655440001", "contents": [{"content": "<h2>Müşteri Hizmetleri Rehberi</h2><p>Müşteri hizmetleri süreçleri ve standartları.</p>", "order": 1}]}, {"title": "Teknik Dokümantasyon", "slug": "teknik-dokumantasyon", "type": "technical-docs", "icon": "FileText", "order": 1, "status": 1, "publishDate": "2025-07-03T16:00:00Z", "languageId": "550e8400-e29b-41d4-a716-446655440000", "groupId": "550e8400-e29b-41d4-a716-446655440001", "contents": [{"content": "<h2>Teknik Dokümantasyon</h2><p>Sistem ve uygulama teknik dokümantasyonu.</p>", "order": 1}]}, {"title": "Eğitim Materyalleri", "slug": "egitim-<PERSON><PERSON><PERSON><PERSON>", "type": "training-materials", "icon": "BookOpen", "order": 1, "status": 1, "publishDate": "2025-07-03T16:00:00Z", "languageId": "550e8400-e29b-41d4-a716-446655440000", "groupId": "550e8400-e29b-41d4-a716-446655440001", "contents": [{"content": "<h2>Eğitim Materyalleri</h2><p>Personel eğitimi için hazırlanmış materyaller.</p>", "order": 1}]}]}}]}