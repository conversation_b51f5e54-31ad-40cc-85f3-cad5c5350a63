# Pages API Kullanım Örnekleri

## 1. CreatePage API Kullanımı

### Endpoint
```
POST /api/v1/Pages
Content-Type: application/json
```

### Örnek 1: Tek <PERSON>şturma
```json
{
  "pages": [
    {
      "title": "Güvenlik Prosedürleri",
      "slug": "guvenlik-prosedurleri",
      "type": "security-procedures",
      "icon": "Shield",
      "order": 1,
      "status": 1,
      "publishDate": "2025-07-03T16:00:00Z",
      "languageId": "550e8400-e29b-41d4-a716-************",
      "groupId": "550e8400-e29b-41d4-a716-************",
      "imageId": "550e8400-e29b-41d4-a716-446655440002",
      "contents": [
        {
          "content": "<h2>Güvenlik Prosedürleri</h2><p><PERSON>u sayfada güvenlik ile ilgili tüm prosedürler yer almaktadır.</p>",
          "order": 1
        }
      ]
    }
  ]
}
```

### Örnek 2: Çoklu Sayfa Oluşturma (Aynı Tip)
```json
{
  "pages": [
    {
      "title": "Temel Güvenlik Kuralları",
      "slug": "temel-guvenlik-kurallari",
      "type": "security-procedures",
      "icon": "Shield",
      "order": 1,
      "status": 1,
      "publishDate": "2025-07-03T16:00:00Z",
      "languageId": "550e8400-e29b-41d4-a716-************",
      "groupId": "550e8400-e29b-41d4-a716-************",
      "contents": [
        {
          "content": "<h2>Temel Güvenlik Kuralları</h2><p>Herkesin bilmesi gereken temel güvenlik kuralları.</p>",
          "order": 1
        }
      ]
    },
    {
      "title": "Gelişmiş Güvenlik Protokolleri",
      "slug": "gelismis-guvenlik-protokolleri",
      "type": "security-procedures",
      "icon": "ShieldCheck",
      "order": 2,
      "status": 1,
      "publishDate": "2025-07-03T16:00:00Z",
      "languageId": "550e8400-e29b-41d4-a716-************",
      "groupId": "550e8400-e29b-41d4-a716-************",
      "contents": [
        {
          "content": "<h2>Gelişmiş Güvenlik Protokolleri</h2><p>İleri düzey güvenlik protokolleri ve uygulamaları.</p>",
          "order": 1
        }
      ]
    }
  ]
}
```

## 2. GetPages API Kullanımı (Type Filtering)

### Endpoint
```
POST /api/v1/Pages
Content-Type: application/json
```

### Örnek 1: Tipe Göre Filtreleme
```json
{
  "groupId": "550e8400-e29b-41d4-a716-************",
  "languageId": "550e8400-e29b-41d4-a716-************",
  "type": "security-procedures"
}
```

### Örnek 2: Sadece Tip Filtresi
```json
{
  "type": "customer-service"
}
```

### Örnek 3: Tüm Filtreler
```json
{
  "groupId": "550e8400-e29b-41d4-a716-************",
  "languageId": "550e8400-e29b-41d4-a716-************",
  "type": "technical-docs"
}
```

### Beklenen Response
```json
{
  "data": [
    {
      "id": "guid-here",
      "groupId": "550e8400-e29b-41d4-a716-************",
      "languageId": "550e8400-e29b-41d4-a716-************",
      "title": "Güvenlik Prosedürleri",
      "slug": "guvenlik-prosedurleri",
      "type": "security-procedures",
      "icon": "Shield",
      "order": 1,
      "publishDate": "2025-07-03T16:00:00Z",
      "subPages": [],
      "pageContents": [],
      "file": null,
      "localizedSlugs": {},
      "jointGroup": [],
      "status": 1,
      "createdAt": "2025-07-03T16:00:00Z",
      "updatedAt": "2025-07-03T16:00:00Z"
    }
  ],
  "isSuccessful": true,
  "statusCode": 200,
  "message": null
}
```

## 3. FindPageBySlug API Kullanımı (JointGroup ile)

### Endpoint
```
POST /api/v1/Pages/FindPageBySlug
Content-Type: application/json
```

### Request
```json
{
  "slug": "guvenlik-prosedurleri"
}
```

### Beklenen Response (JointGroup ile)
```json
{
  "data": {
    "id": "guid-here",
    "groupId": "550e8400-e29b-41d4-a716-************",
    "languageId": "550e8400-e29b-41d4-a716-************",
    "title": "Güvenlik Prosedürleri",
    "slug": "guvenlik-prosedurleri",
    "type": "security-procedures",
    "icon": "Shield",
    "order": 1,
    "publishDate": "2025-07-03T16:00:00Z",
    "subPages": [],
    "pageContents": [
      {
        "id": "content-guid",
        "content": "<h2>Güvenlik Prosedürleri</h2><p>Bu sayfada güvenlik ile ilgili tüm prosedürler yer almaktadır.</p>",
        "order": 1
      }
    ],
    "file": null,
    "localizedSlugs": {},
    "jointGroup": [
      {
        "title": "Temel Güvenlik Kuralları",
        "icon": "Shield",
        "slug": "temel-guvenlik-kurallari"
      },
      {
        "title": "Gelişmiş Güvenlik Protokolleri",
        "icon": "ShieldCheck",
        "slug": "gelismis-guvenlik-protokolleri"
      },
      {
        "title": "Acil Durum Güvenlik Prosedürleri",
        "icon": "AlertTriangle",
        "slug": "acil-durum-guvenlik-prosedurleri"
      }
    ],
    "status": 1,
    "createdAt": "2025-07-03T16:00:00Z",
    "updatedAt": "2025-07-03T16:00:00Z"
  },
  "isSuccessful": true,
  "statusCode": 200,
  "message": null
}
```

## 4. UpdatePage API Kullanımı

### Endpoint
```
PUT /api/v1/Pages
Content-Type: application/json
```

### Request
```json
{
  "pages": [
    {
      "id": "existing-page-guid",
      "title": "Güncellenmiş Güvenlik Prosedürleri",
      "slug": "guncellenmiş-guvenlik-prosedurleri",
      "type": "security-procedures",
      "icon": "ShieldAlert",
      "order": 1,
      "status": 1,
      "publishDate": "2025-07-03T16:00:00Z",
      "languageId": "550e8400-e29b-41d4-a716-************",
      "groupId": "550e8400-e29b-41d4-a716-************",
      "contents": [
        {
          "content": "<h2>Güncellenmiş Güvenlik Prosedürleri</h2><p>Yeni güvenlik prosedürleri ve güncellemeler.</p>",
          "order": 1
        }
      ]
    }
  ]
}
```

## 5. Yaygın Type ve Icon Örnekleri

### Önerilen Type Değerleri
- `security-procedures` - Güvenlik prosedürleri
- `service-procedures` - Hizmet prosedürleri
- `emergency-procedures` - Acil durum prosedürleri
- `customer-service` - Müşteri hizmetleri
- `technical-docs` - Teknik dokümantasyon
- `training-materials` - Eğitim materyalleri
- `policy-documents` - Politika belgeleri
- `user-guides` - Kullanıcı rehberleri
- `faq-pages` - Sık sorulan sorular
- `news-announcements` - Haberler ve duyurular

### Önerilen Icon Değerleri
- `Shield` - Güvenlik
- `Settings` - Ayarlar/Hizmetler
- `AlertTriangle` - Acil durum
- `Users` - Müşteri hizmetleri
- `FileText` - Dokümantasyon
- `BookOpen` - Eğitim
- `Clipboard` - Politikalar
- `HelpCircle` - Yardım/FAQ
- `Bell` - Duyurular
- `CheckSquare` - Kontrol listeleri

## 6. Curl Örnekleri

### CreatePage
```bash
curl -X POST "https://your-api-domain/api/v1/Pages" \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer your-token" \
  -d '{
    "pages": [
      {
        "title": "Güvenlik Prosedürleri",
        "slug": "guvenlik-prosedurleri",
        "type": "security-procedures",
        "icon": "Shield",
        "order": 1,
        "status": 1,
        "publishDate": "2025-07-03T16:00:00Z",
        "languageId": "550e8400-e29b-41d4-a716-************",
        "groupId": "550e8400-e29b-41d4-a716-************"
      }
    ]
  }'
```

### GetPages with Type Filter
```bash
curl -X POST "https://your-api-domain/api/v1/Pages" \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer your-token" \
  -d '{
    "type": "security-procedures"
  }'
```

### FindPageBySlug
```bash
curl -X POST "https://your-api-domain/api/v1/Pages/FindPageBySlug" \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer your-token" \
  -d '{
    "slug": "guvenlik-prosedurleri"
  }'
```
