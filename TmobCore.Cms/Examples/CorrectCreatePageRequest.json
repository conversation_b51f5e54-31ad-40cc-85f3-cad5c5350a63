{"correctRequest": {"endpoint": "POST /api/v1/Pages/CreatePage", "headers": {"Content-Type": "application/json", "Authorization": "Bearer your-token"}, "body": {"pages": [{"title": "Güvenlik Prosedürleri Test", "slug": "guvenlik-prosedurleri-test", "type": "procedures", "icon": "Shield", "order": 1, "status": 1, "publishDate": "2025-07-03T16:00:00Z", "languageId": "82cc55c1-6d05-40bb-f557-08dd5be25c9d", "groupId": "550e8400-e29b-41d4-a716-446655440001", "contents": [{"content": "<h2>Güvenlik Prosedürleri</h2><p>Bu sayfada güvenlik ile ilgili tüm prosedürler yer almaktadır.</p>", "order": 1}]}, {"title": "Hizmet Prosedürleri Test", "slug": "hizmet-prosedurleri-test", "type": "procedures", "icon": "Settings", "order": 2, "status": 1, "publishDate": "2025-07-03T16:00:00Z", "languageId": "82cc55c1-6d05-40bb-f557-08dd5be25c9d", "groupId": "550e8400-e29b-41d4-a716-446655440011", "contents": [{"content": "<h2>Hizmet Prosedürleri</h2><p>Hizmet süreçleri ve prosedürleri hakkında bilgiler.</p>", "order": 1}]}, {"title": "Acil Durum Prosedürleri Test", "slug": "acil-durum-prosedurleri-test", "type": "procedures", "icon": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "order": 3, "status": 1, "publishDate": "2025-07-03T16:00:00Z", "languageId": "82cc55c1-6d05-40bb-f557-08dd5be25c9d", "groupId": "550e8400-e29b-41d4-a716-446655440032", "contents": [{"content": "<h2>Acil Durum Prosedürleri</h2><p>Acil durumlar için u<PERSON>gulanması gereken prosedürler.</p>", "order": 1}]}]}}, "yourIncorrectRequest": {"issues": ["Extra nesting with 'description' and 'request' objects", "Using wrong endpoint POST /api/v1/Pages instead of POST /api/v1/Pages/CreatePage", "JSON structure doesn't match CreatePageCommand model"], "whatYouSent": {"pages": [{"description": "...", "request": {"pages": ["// This extra nesting breaks model binding"]}}]}}}