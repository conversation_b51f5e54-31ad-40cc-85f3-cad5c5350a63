using TmobCore.Cms.Application.Features.Page.Commands.CreatePage;
using TmobCore.Cms.Application.Models.Common;
using TmobCore.Cms.Application.Models.Page;

namespace TmobCore.Cms.Examples
{
    /// <summary>
    /// CreatePage request örnekleri - Yeni Type ve Icon alanları ile
    /// </summary>
    public static class CreatePageRequestExamples
    {
        /// <summary>
        /// Örnek 1: Güvenlik prosedürleri sayfası oluşturma
        /// </summary>
        public static CreatePageCommand CreateSecurityProceduresPage()
        {
            return new CreatePageCommand
            {
                Pages = new List<CreatePageRequest>
                {
                    new CreatePageRequest
                    {
                        Title = "Güvenlik Prosedürleri",
                        Slug = "guvenlik-prosedurleri",
                        Type = "security-procedures",
                        Icon = "Shield",
                        Order = 1,
                        Status = BaseStatus.Published,
                        PublishDate = DateTime.Now,
                        LanguageId = Guid.Parse("550e8400-e29b-41d4-a716-************"),
                        GroupId = Guid.Parse("550e8400-e29b-41d4-a716-************"),
                        ImageId = Guid.Parse("550e8400-e29b-41d4-a716-************"),
                        Contents = new List<PageContentRequest>
                        {
                            new PageContentRequest
                            {
                                Content = "<h2>Güvenlik Prosedürleri</h2><p>Bu sayfada güvenlik ile ilgili tüm prosedürler yer almaktadır.</p>",
                                Order = 1
                            }
                        }
                    }
                }
            };
        }

        /// <summary>
        /// Örnek 2: Hizmet prosedürleri sayfası oluşturma
        /// </summary>
        public static CreatePageCommand CreateServiceProceduresPage()
        {
            return new CreatePageCommand
            {
                Pages = new List<CreatePageRequest>
                {
                    new CreatePageRequest
                    {
                        Title = "Hizmet Prosedürleri",
                        Slug = "hizmet-prosedurleri",
                        Type = "service-procedures",
                        Icon = "Settings",
                        Order = 2,
                        Status = BaseStatus.Published,
                        PublishDate = DateTime.Now,
                        LanguageId = Guid.Parse("550e8400-e29b-41d4-a716-************"),
                        GroupId = Guid.Parse("550e8400-e29b-41d4-a716-************"),
                        Contents = new List<PageContentRequest>
                        {
                            new PageContentRequest
                            {
                                Content = "<h2>Hizmet Prosedürleri</h2><p>Hizmet süreçleri ve prosedürleri hakkında bilgiler.</p>",
                                Order = 1
                            }
                        }
                    }
                }
            };
        }

        /// <summary>
        /// Örnek 3: Çoklu sayfa oluşturma - Aynı tip (security-procedures)
        /// Bu örnekte aynı tipte birden fazla sayfa oluşturuyoruz
        /// FindPageBySlug çağrıldığında jointGroup'ta birbirlerini gösterecekler
        /// </summary>
        public static CreatePageCommand CreateMultipleSecurityPages()
        {
            return new CreatePageCommand
            {
                Pages = new List<CreatePageRequest>
                {
                    new CreatePageRequest
                    {
                        Title = "Temel Güvenlik Kuralları",
                        Slug = "temel-guvenlik-kurallari",
                        Type = "security-procedures",
                        Icon = "Shield",
                        Order = 1,
                        Status = BaseStatus.Published,
                        PublishDate = DateTime.Now,
                        LanguageId = Guid.Parse("550e8400-e29b-41d4-a716-************"),
                        GroupId = Guid.Parse("550e8400-e29b-41d4-a716-************"),
                        Contents = new List<PageContentRequest>
                        {
                            new PageContentRequest
                            {
                                Content = "<h2>Temel Güvenlik Kuralları</h2><p>Herkesin bilmesi gereken temel güvenlik kuralları.</p>",
                                Order = 1
                            }
                        }
                    },
                    new CreatePageRequest
                    {
                        Title = "Gelişmiş Güvenlik Protokolleri",
                        Slug = "gelismis-guvenlik-protokolleri",
                        Type = "security-procedures",
                        Icon = "ShieldCheck",
                        Order = 2,
                        Status = BaseStatus.Published,
                        PublishDate = DateTime.Now,
                        LanguageId = Guid.Parse("550e8400-e29b-41d4-a716-************"),
                        GroupId = Guid.Parse("550e8400-e29b-41d4-a716-************"),
                        Contents = new List<PageContentRequest>
                        {
                            new PageContentRequest
                            {
                                Content = "<h2>Gelişmiş Güvenlik Protokolleri</h2><p>İleri düzey güvenlik protokolleri ve uygulamaları.</p>",
                                Order = 1
                            }
                        }
                    },
                    new CreatePageRequest
                    {
                        Title = "Acil Durum Güvenlik Prosedürleri",
                        Slug = "acil-durum-guvenlik-prosedurleri",
                        Type = "security-procedures",
                        Icon = "AlertTriangle",
                        Order = 3,
                        Status = BaseStatus.Published,
                        PublishDate = DateTime.Now,
                        LanguageId = Guid.Parse("550e8400-e29b-41d4-a716-************"),
                        GroupId = Guid.Parse("550e8400-e29b-41d4-a716-************"),
                        Contents = new List<PageContentRequest>
                        {
                            new PageContentRequest
                            {
                                Content = "<h2>Acil Durum Güvenlik Prosedürleri</h2><p>Acil durumlar için uygulanması gereken güvenlik prosedürleri.</p>",
                                Order = 1
                            }
                        }
                    }
                }
            };
        }

        /// <summary>
        /// Örnek 4: Farklı kategorilerde sayfalar oluşturma
        /// </summary>
        public static CreatePageCommand CreateDifferentCategoryPages()
        {
            return new CreatePageCommand
            {
                Pages = new List<CreatePageRequest>
                {
                    new CreatePageRequest
                    {
                        Title = "Müşteri Hizmetleri Rehberi",
                        Slug = "musteri-hizmetleri-rehberi",
                        Type = "customer-service",
                        Icon = "Users",
                        Order = 1,
                        Status = BaseStatus.Published,
                        PublishDate = DateTime.Now,
                        LanguageId = Guid.Parse("550e8400-e29b-41d4-a716-************"),
                        GroupId = Guid.Parse("550e8400-e29b-41d4-a716-************"),
                        Contents = new List<PageContentRequest>
                        {
                            new PageContentRequest
                            {
                                Content = "<h2>Müşteri Hizmetleri Rehberi</h2><p>Müşteri hizmetleri süreçleri ve standartları.</p>",
                                Order = 1
                            }
                        }
                    },
                    new CreatePageRequest
                    {
                        Title = "Teknik Dokümantasyon",
                        Slug = "teknik-dokumantasyon",
                        Type = "technical-docs",
                        Icon = "FileText",
                        Order = 1,
                        Status = BaseStatus.Published,
                        PublishDate = DateTime.Now,
                        LanguageId = Guid.Parse("550e8400-e29b-41d4-a716-************"),
                        GroupId = Guid.Parse("550e8400-e29b-41d4-a716-************"),
                        Contents = new List<PageContentRequest>
                        {
                            new PageContentRequest
                            {
                                Content = "<h2>Teknik Dokümantasyon</h2><p>Sistem ve uygulama teknik dokümantasyonu.</p>",
                                Order = 1
                            }
                        }
                    },
                    new CreatePageRequest
                    {
                        Title = "Eğitim Materyalleri",
                        Slug = "egitim-materyalleri",
                        Type = "training-materials",
                        Icon = "BookOpen",
                        Order = 1,
                        Status = BaseStatus.Published,
                        PublishDate = DateTime.Now,
                        LanguageId = Guid.Parse("550e8400-e29b-41d4-a716-************"),
                        GroupId = Guid.Parse("550e8400-e29b-41d4-a716-************"),
                        Contents = new List<PageContentRequest>
                        {
                            new PageContentRequest
                            {
                                Content = "<h2>Eğitim Materyalleri</h2><p>Personel eğitimi için hazırlanmış materyaller.</p>",
                                Order = 1
                            }
                        }
                    }
                }
            };
        }

        /// <summary>
        /// Örnek 5: Alt sayfa oluşturma (ParentPageId ile)
        /// </summary>
        public static CreatePageCommand CreateSubPage(Guid parentPageId)
        {
            return new CreatePageCommand
            {
                Pages = new List<CreatePageRequest>
                {
                    new CreatePageRequest
                    {
                        Title = "Güvenlik Kontrol Listesi",
                        Slug = "guvenlik-kontrol-listesi",
                        Type = "security-procedures",
                        Icon = "CheckSquare",
                        ParentPageId = parentPageId,
                        Order = 1,
                        Status = BaseStatus.Published,
                        PublishDate = DateTime.Now,
                        LanguageId = Guid.Parse("550e8400-e29b-41d4-a716-************"),
                        GroupId = Guid.Parse("550e8400-e29b-41d4-a716-************"),
                        Contents = new List<PageContentRequest>
                        {
                            new PageContentRequest
                            {
                                Content = "<h2>Güvenlik Kontrol Listesi</h2><p>Günlük güvenlik kontrolleri için kullanılacak liste.</p>",
                                Order = 1
                            }
                        }
                    }
                }
            };
        }
    }
}
